package com.ainirobot.platform.react

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Outline
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Process
import android.os.SystemClock
import android.text.TextUtils
import android.util.Log
import android.view.*
import com.ainirobot.base.util.SystemUtils
import com.ainirobot.coreservice.client.Definition
import com.ainirobot.coreservice.client.ProductInfo
import com.ainirobot.coreservice.client.RobotSettings
import com.ainirobot.coreservice.client.actionbean.BatteryBean
import com.ainirobot.emoji.SwitchEmojiManager
import com.ainirobot.platform.BaseApplication
import com.ainirobot.platform.R
import com.ainirobot.platform.appmanager.AppInfo
import com.ainirobot.platform.bi.annotation.OpkOpStatus
import com.ainirobot.platform.bi.annotation.OpkOpType
import com.ainirobot.platform.bi.wrapper.ReportControl
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.OpkOperationPoint
import com.ainirobot.platform.bi.wrapper.manager.bi.impl.RnDownloadTargetPoint
import com.ainirobot.platform.react.BatteryWarningDialog.BatteryWarningListener
import com.ainirobot.platform.react.client.ClientUtils
import com.ainirobot.platform.react.client.RNClientManager
import com.ainirobot.platform.react.network.NetworkManger
import com.ainirobot.platform.react.reactnative.character.DownloadCoreBundleBuilder
import com.ainirobot.platform.react.reactnative.character.FileCoreListener
import com.ainirobot.platform.react.reactnative.character.ReactCharacter
import com.ainirobot.platform.react.reactnative.character.StartEvent
import com.ainirobot.platform.react.reactnative.component.uicomponent.statusbar.StatusBarManager
import com.ainirobot.platform.react.reactnative.component.utils.ReactNativeEventEmitter
import com.ainirobot.platform.rn.listener.IRNRobotStatusListener
import com.ainirobot.platform.rn.listener.IRNUpdateListener
import com.ainirobot.platform.utils.BackgroundThread
import com.ainirobot.platform.utils.FileHelper
import com.ainirobot.platform.utils.ReactHelper
import com.ainirobot.platform.utils.SystemUtils.isActivityTop
import com.ainirobot.platform.utils.WebUtil
import com.ainirobot.platform.utils.imageloader.Md5
import com.facebook.react.ReactActivity2
import com.facebook.react.bridge.BridgeUtil
import com.facebook.react.bridge.WritableNativeMap
import com.google.gson.Gson
import com.liulishuo.filedownloader.FileDownloader
import com.liulishuo.filedownloader.util.FileDownloadLog
import org.json.JSONObject
import org.simple.eventbus.EventBus
import org.simple.eventbus.Subscriber
import org.simple.eventbus.ThreadMode
import java.io.File
import java.lang.ref.WeakReference
import java.util.*
import kotlin.math.abs

class EveActivity : ReactActivity2() {

    companion object {
        lateinit var currentActivity: WeakReference<EveActivity>
        lateinit var handler: Handler
        lateinit var updateListener: IRNUpdateListener.Stub
        const val HANDLE_ERROR = -1
        const val HANDLE_CENTER_KEY_SHORT = 0
        const val HANDLE_UP_KEY = 1
        const val HANDLE_DOWN_KEY = 2
        const val HANDLE_LEFT_KEY = 3
        const val HANDLE_RIGHT_KEY = 4
        const val HANDLE_CENTER_KEY_LONG = 5
        const val HANDLE_CENTER_KEY = 6
        const val DOUBLE_CLICK_TIME_DELTA: Long = 300 // 双击时间间隔阈值，单位为毫秒
        const val EVENT_NAME = "handle_key_event"

        @JvmStatic
        fun getActivity(): WeakReference<EveActivity> {
            return currentActivity
        }

        const val TAG = "EveActivity"
    }

    private var mFailedDialog: Dialog? = null
    var mFirstCheckBattery: Boolean = false;
    var mBatteryWarningDialog: BatteryWarningDialog? = null
    var mBatteryNotifyDialog: BatteryNotifyDialog? = null
    var mHasShowBatteryWarnDialog: Boolean = false


    /**
     * Returns the name of the main component registered from JavaScript.
     * This is used to schedule rendering of the component.
     */
    override fun getMainComponentName(): String? {
        return "Eve"
    }

    @SuppressLint("MissingSuperCall")
    override fun onCreate(savedInstanceState: Bundle?) {
        Log.i(TAG, "onCreate")
        WebUtil.hookWebView()
        currentActivity = WeakReference(this)
        //单纯ActivityCreate
        super.onActivityCreate(savedInstanceState)

        EventBus.getDefault().register(this)

        val reqId = intent.getIntExtra("extra_reqId", -1)
        val msgId = intent.getStringExtra("extra_msgId")
        val name = intent.getStringExtra("extra_name")
        Log.d(TAG, "onCreate reqId: $reqId msgId: $msgId name: $name")

        val handler = Handler()
        handler.post(object : Runnable {
            override fun run() {
                if (RNClientManager.instance!!.updateManager != null) {
                    Log.d(TAG, "Check preset status : ${RNClientManager.instance!!.updateManager!!.isNeedUpdatePresetApp}")
                    if (RNClientManager.instance!!.updateManager!!.isNeedUpdatePresetApp) {
                        startInstallPresetApp(reqId, msgId)
                    } else {
                        tryStartEve(reqId, msgId)
                    }
                } else {
                    handler.postDelayed(this, 100)
                }
            }
        })
        StatusBarManager.getStatusBar()?.onCreate();
//        StatusBar.getInstance().onCreate()
    }

    private fun startInstallPresetApp(reqId: Int, msgId: String?) {
        Log.d(TAG, "Start install preset app")
        showLoadingView()
        updateListener = object : IRNUpdateListener.Stub() {
            override fun onProgressUpdate(count: Int, index: Int, appName: String?, oper: String?) {
            }

            override fun onFinished(result: Boolean, message: String?) {
                Log.d(TAG, "On preset finished : $result")
                if (result) {
                    runOnUiThread {
                        hideLoadingView()
                        mFailedDialog?.dismiss()
                    }
                    if (!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus()) {
                        AppManger.reload();
                    }
                    tryStartEve(reqId, msgId)
                } else {
                    runOnUiThread {
                        showFailedDialog()
                    }
                }
            }

            override fun onConfigUpdate(appId: String?, newConfig: String?) {
            }

            override fun onStart() {

            }
        }
        RNClientManager.instance!!.updateManager!!.setPresetAppListener(updateListener)
        RNClientManager.instance!!.updateManager!!.startUpdate()
    }

    /**
     * 设置OPK更新监听
     *
     * Activity启动过程中，OPK进行了更新，需要重启Activity进行更新
     */
    private fun setUpdateListener() {
        updateListener = object : IRNUpdateListener.Stub() {
            override fun onProgressUpdate(count: Int, index: Int, appName: String?, oper: String?) {
            }

            override fun onFinished(result: Boolean, message: String?) {
                val data = JSONObject(message);
                val isNeedReboot = data.getBoolean("isNeedReboot");
                if (isNeedReboot) {
                    runOnUiThread {
                        recreate()
                    }
                }
            }

            override fun onConfigUpdate(appId: String?, newConfig: String?) {
            }

            override fun onStart() {
            }
        }
        RNClientManager.instance!!.updateManager!!.setUpdateListener(updateListener)
    }

    private fun showFailedDialog() {
        Log.d(TAG, "Show failed dialog")
        hideLoadingView()
        mFailedDialog = Dialog(this@EveActivity)

        val view = LayoutInflater.from(this@EveActivity).inflate(R.layout.platform_load_failed, null, false)
        view.outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View?, outline: Outline?) {
                view!!.clipToOutline = true
                outline!!.setRoundRect(0, 0, view.width, view.height, 60f)
            }
        }
        view.findViewById(R.id.retry).setOnClickListener {
            mFailedDialog!!.dismiss()
            showLoadingView()
            RNClientManager.instance!!.updateManager!!.retryUpdate()
        }

        mFailedDialog!!.setContentView(view)
        val params = mFailedDialog!!.window?.attributes
        params?.width = 950
        params?.height = WindowManager.LayoutParams.WRAP_CONTENT
        mFailedDialog!!.window?.attributes = params

        mFailedDialog!!.setCanceledOnTouchOutside(false)

        mFailedDialog!!.show()
    }

    private fun showLoadingView() {
        Log.d(TAG, "Show loading view")

        //如果水印没有添加则 添加水印
        val rootView: ViewGroup = findViewById(android.R.id.content) as ViewGroup
        val view = LayoutInflater.from(this@EveActivity).inflate(R.layout.platform_eve_main, rootView, false)
        rootView.addView(view)
    }

    private fun hideLoadingView() {
        Log.d(TAG, "Hide loading view")
        val loadView = findViewById<View>(R.id.appLoading) ?: return
        val rootView: ViewGroup = findViewById(android.R.id.content) as ViewGroup
        rootView.removeView(loadView)
    }

    private fun tryStartEve(reqId: Int, msgId: String?) {
        handler.post(object : Runnable {
            override fun run() {
                if (RNClientManager.instance!!.mRNManagerService != null) {
                    startEve(reqId, msgId)
                } else {
                    handler.postDelayed(this, 100)
                }
            }
        })
    }

    private fun startEve(reqId: Int, msgId: String?) {

        val currentCharcater = RNClientManager.instance?.dataManager?.currentCharcater

        ReactCharacter.currentCharacter = currentCharcater
        Log.i(TAG, "opk $currentCharcater name: is valid ${BaseApplication.getApplication().reactNativeHost.useDeveloperSupport}")
        //当前没有角色且UseDeveloperSupport 则使用hot reload 模式
        if (BaseApplication.getApplication().reactNativeHost.useDeveloperSupport) {
            loadReactEnvironmentDeveloperSupport(true)
            return
        }

        Log.i(TAG, "opk $currentCharcater name: is valid")
        if (currentCharcater.isNullOrEmpty()) {
            ClientUtils.killProcess(Process.myPid())
            return
        }

        //开始加载前，先设置OPK更新监听
        setUpdateListener()

        var tmpMsgId = ""
        if (!msgId.isNullOrEmpty()) {
            tmpMsgId = msgId
        }

        val startEvent = StartEvent(false, reqId, tmpMsgId, currentCharcater)
        val opkBeanV2 = AppManger.getRPKByCharacter(currentCharcater)

        OPKHelper.onHandleWithOPK(opkBeanV2!!, {
            loadReactEnvironmentV1(true, startEvent)
        }, {
            loadReactEnvironmentV2(true, startEvent)
        }, {
            loadReactEnvironmentV3(true, startEvent)
        })
        RNClientManager.instance!!.apiManager!!.startVision(null)
    }

    @SuppressLint("MissingSuperCall")
    override fun onResume() {
        super.onActivityResume()
        //当跳出到别的应用后续如果ReactContext存在需要执行onReactResume
        if (RNClientManager.instance?.mRNManagerService != null
            && BaseApplication.getApplication().reactNativeHost.reactInstanceManager?.currentReactContext != null) {
            Log.i(TAG, "onReactResume")
            super.onReactResume()
        }
        startEmojiView()
        //window.setBackgroundDrawableResource(R.drawable.main_bg)
        Log.i(TAG, "onResume")
        SwitchEmojiManager.getInstance().onResume()
        val handler = Handler()
        handler.postDelayed({
            if (ProductInfo.isDeliveryProduct()) {
                if (RNClientManager.instance?.apiManager != null) {
                    RNClientManager.instance?.apiManager?.registerStatusListener(Definition.STATUS_BATTERY, mBatteryListener)
                }
            }
        }, 1000)
    }

    private val mBatteryListener = object : IRNRobotStatusListener.Stub() {
        override fun onStatusUpdate(status: String, data: String?) {
            if (status == Definition.STATUS_BATTERY) {
                if (data == null) {
                    return
                }
                val gson = Gson()
                val batteryBean: BatteryBean? = gson.fromJson(data, BatteryBean::class.java)
                if (batteryBean == null) {
                    return
                }
                val hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
                if (!batteryBean.isCharging && !mFirstCheckBattery && batteryBean.level > 10) {
                    Log.i(TAG, "mBatteryListener level: " + batteryBean.level + ", hour: " + hour)
                    var isActivityTop = isActivityTop(EveActivity::class.java,this@EveActivity);
                    Log.i(TAG, "mBatteryListener is EveActivity top: " + isActivityTop)
                    //mFirstCheckBattery = true
                    var showDialog = false
                    var isImmediate = false
                    if (hour in 8..12 && batteryBean.level <= 70) {
                        showDialog = true
                    } else if (hour in 13..15 && batteryBean.level <= 50) {
                        showDialog = true
                    } else if (hour in 16..18 && batteryBean.level <= 30) {
                        showDialog = true
                        isImmediate = true
                    }
                    if (showDialog && isActivityTop) {
                        val handler = Handler(Looper.getMainLooper())
                        handler.post {
                            if (mBatteryNotifyDialog != null) {
                                //mBatteryNotifyDialog?.dismiss()
                                mBatteryNotifyDialog?.updateBatteryRemaining(batteryBean.level, isImmediate);
                            }else{
                                mBatteryNotifyDialog = BatteryNotifyDialog(BaseApplication.getContext())
                                    .setBatteryRemaining(batteryBean.level, isImmediate)
                                    .setBatteryNotifyListener(object : BatteryNotifyDialog.BatteryNotifyListener() {
                                        override fun onStop() {
                                        }
                                    })
                                mBatteryNotifyDialog?.show()
                            }
                        }
                    }
                }
                if (batteryBean.level > 10 || batteryBean.isCharging) {
                    if (mBatteryWarningDialog != null) {
                        mBatteryWarningDialog?.dismiss()
                        mBatteryWarningDialog = null
                    }
                    mHasShowBatteryWarnDialog = false
                } else if (batteryBean.level <= 10 && !batteryBean.isCharging && !mHasShowBatteryWarnDialog) {
                    mHasShowBatteryWarnDialog = true
                    val handler = Handler(Looper.getMainLooper())
                    handler.post {
                        if (mBatteryWarningDialog != null) {
                            mBatteryWarningDialog?.dismiss()
                        }
                        if (mBatteryNotifyDialog != null) {
                            mBatteryNotifyDialog?.dismiss()
                            mBatteryNotifyDialog = null
                        }
                        mBatteryWarningDialog = BatteryWarningDialog(BaseApplication.getContext())
                            .setBatteryRemaining(batteryBean.level)
                            .setBatteryWarningListener(object : BatteryWarningListener() {
                                override fun onStop() {
                                    if (batteryBean.level <= 10 && !batteryBean.isCharging) {
                                        handler.postDelayed({
                                            mHasShowBatteryWarnDialog = false
                                        }, 5 * 60 * 1000)
                                    }
                                }
                            })
                        mBatteryWarningDialog?.show()
                    }
                }
            }
        }
    }

    /**
     * UseDeveloperSupport 阶段的ReactNative的环境加载
     * */
    private fun loadReactEnvironmentDeveloperSupport(isCreate: Boolean) {
        Log.i(TAG, "loadReactEnvironmentDeveloperSupport $isCreate")
        if (isCreate) {
            //React 页面创建 第一次时需要创建ReactRootView
            <EMAIL>(null)
        } else {
            //reload环境
            ReactHelper.reloadReactJS(BaseApplication.getApplication().reactNativeHost.reactInstanceManager)
        }
        //React Resume状态
        <EMAIL>()
        addWaterMark()
    }

    /**
     * 加载RN环境 V1 整包版本
     * */
    private fun loadReactEnvironmentV1(isCreate: Boolean, startEvent: StartEvent = StartEvent(false, -1, "", "")) {
        Log.i(TAG, "loadReactEnvironmentV1 $startEvent")
        Log.i(TAG, "loadReactEnvironmentV1 $isCreate")
        val indexBundlePath = AppManger.getAppIndexPathByCharacter(startEvent.name)
        val opkBeanV2 = AppManger.getRPKByCharacter(startEvent.name)
        Log.i(TAG, "indexBundlePath $indexBundlePath")
        val md5 = Md5.generateFileMD5(indexBundlePath)
        Log.i(TAG, "md5: $md5")
        if (indexBundlePath.isNullOrEmpty()) {
            reportOpkOpCmd(startEvent.msgId, startEvent.name, OpkOpStatus.RN_OPK_START_FAILED)
        }
        val backupFile = File(OPKHelper.getBackupPath(opkBeanV2?.appid))
        if (backupFile.exists() && opkBeanV2?.bizMd5 != null && opkBeanV2?.bizMd5 != Md5.generateFileMD5(indexBundlePath)) {
            Log.i(TAG, "opk v1 bundle use backup")
            //解压备份文件
            FileHelper.unzip(backupFile.absolutePath, File(indexBundlePath).parent)
        }
        ReactHelper.loadReactJS(BaseApplication.getApplication().reactNativeHost.reactInstanceManager, indexBundlePath!!, isCreate) {
            if (isCreate) {
                //React 页面创建 第一次时需要创建ReactRootView
                <EMAIL>(null)
            }
            //React Resume状态
            <EMAIL>()
            if (!startEvent.isCrash) {
                //上报切换opk是否成功
                RNClientManager.instance?.dataManager?.reportRNSwitch(1)
            }

            if (!TextUtils.isEmpty(startEvent.msgId)) {
                notifyToServer(startEvent.msgId, startEvent.name, 0)
                reportOpkOpCmd(startEvent.msgId, startEvent.name, OpkOpStatus.RN_OPK_START_SUCCESS)
            }

            addWaterMark()
        }

    }

    private fun reportOpkOpCmd(msgId: String, appid: String, @OpkOpStatus status: Int) {
        var errMsg = ""
        var tmpMsgId = msgId
        var tmpAppid = appid

        if (TextUtils.isEmpty(msgId) || TextUtils.isEmpty(appid)) {
            Log.w(TAG, "reportOpkOpCmd: param is error msgId: $msgId appid: $appid")
            errMsg = "param error"
            tmpMsgId = ""
            tmpAppid = ""
        }

        val point = OpkOperationPoint(tmpMsgId, tmpAppid, OpkOpType.LAUNCH_RN, status, errMsg)
        ReportControl.getInstance().reportMsg(point)
    }

    private fun addWaterMark() {
        //如果水印没有添加则 添加水印
//        var view: View? = findViewById(R.id.eve_watermark)
//        val viewgroup: ViewGroup = findViewById(android.R.id.content) as ViewGroup
//        if (view == null) {
//            view = LayoutInflater.from(this@EveActivity).inflate(R.layout.platform_eve_watermark, viewgroup, false)
//            viewgroup.addView(view)
//        }
    }

    private fun addDebugModeMark() {
        var view: View? = findViewById(R.id.debug_eve_watermark)
        val viewgroup: ViewGroup = findViewById(android.R.id.content) as ViewGroup
        if (view == null) {
            view = LayoutInflater.from(this@EveActivity).inflate(R.layout.platform_debug_eve_watermark, viewgroup, false)
            viewgroup.addView(view)
        }
    }

    private fun removeDebugModeMark() {
        var view: View? = findViewById(R.id.debug_eve_watermark)
        val viewgroup: ViewGroup = findViewById(android.R.id.content) as ViewGroup
        if (view != null) {
            viewgroup.removeView(view)
        }
    }


    /**
     * 加载RN环境 V3 插件版本
     * */
    private fun loadReactEnvironmentV3(isCreate: Boolean, startEvent: StartEvent = StartEvent(false, -1, "", "")) {
        Log.i(TAG, "loadReactEnvironmentV3 $startEvent")
        Log.i(TAG, "loadReactEnvironmentV3 $isCreate " + startEvent.isCrash)
        val hostInfo = AppManger.getRPKByCharacter(startEvent.name)

        // 获取当前系统的 OS 类型
        val systemOSType = RobotSettings.getGlobalSettings(BaseApplication.getContext(), Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "")
        Log.i(TAG, "loadReactEnvironmentV3 systemOSType=$systemOSType, host osType=${hostInfo?.osType}")

        val platformBundlePath = AppManger.getAppPlatformPathByCharacter(startEvent.name)
        val hostBundlePath = AppManger.getAppBizPathByCharacter(startEvent.name)

        val pluginBundles = mutableListOf<String?>()

        Log.i(TAG, "loadReactEnvironmentV3 ********** ")
        if (AppInfo.OPK_HOST == hostInfo?.type) {
            val pluginList = AppManger.getAllPluginInfo()
            hostInfo.plugin = pluginList
            val iterator = pluginList.iterator()
            Log.i(TAG, "loadReactEnvironmentV3 ********** ${pluginList.size}")
            while (iterator.hasNext()) {
                val it = iterator.next()
                if (AppManger.isAppValid(it.appid)) {
                    val pluginPath = AppManger.getAppBizPathByCharacter(it.appid)
                    if (pluginPath != null && File(pluginPath).exists()) {

                        Log.i(TAG, "loadReactEnvironmentV3 plugin osType=${it.osType}")

                        // 检查插件的 osType 是否与当前系统类型一致
                        if (!TextUtils.isEmpty(it.osType) && !TextUtils.equals(systemOSType, it.osType)) {
                            Log.i(TAG, "loadReactEnvironmentV3 skip plugin ${it.appid} due to osType mismatch: system=$systemOSType, plugin=${it.osType}")
                            continue
                        }

                        val isSupportElectricDoor = RNClientManager.instance != null && RNClientManager.instance!!.apiManager != null
                                && RNClientManager.instance!!.apiManager!!.isSupportElectricDoor
                        val isSupportKKCamera = RNClientManager.instance != null && RNClientManager.instance!!.apiManager != null
                                && RNClientManager.instance!!.apiManager!!.isSupportKKCamera
                        val isSupportElevator = (RNClientManager.instance != null && RNClientManager.instance!!.apiManager != null
                                && RNClientManager.instance!!.apiManager!!.isSupportElevator) &&
                                (RNClientManager.instance != null && RNClientManager.instance!!.robotSettingManager != null
                                        && RNClientManager.instance!!.robotSettingManager!!.getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED) == Definition.ROBOT_SETTING_ENABLE)
                        val isSupportFactory = RNClientManager.instance != null && RNClientManager.instance!!.robotSettingManager != null
                                        && RNClientManager.instance!!.robotSettingManager!!.getRobotInt("meal_is_factory_scene") == Definition.ROBOT_SETTING_ENABLE
                        Log.i(TAG, "loadReactEnvironmentV3 ********** supportModel= ${it.supportModel} isSupportFactory= ${isSupportFactory}" +
                                " isSupportElectricDoor= ${isSupportElectricDoor} isSupportKKCamera= ${isSupportKKCamera} isSupportElevator= ${isSupportElevator}")
                        when (it.supportModel) {
                            SupportModelEnum.SAIPH_EDOOR.supportModel -> {
                                if (isSupportElectricDoor) {
                                    Log.i(TAG, "loadReactEnvironmentV3 ********** saiph_Edoor appId= ${it.appid} appName= ${it.appName}")
                                    pluginBundles.add(pluginPath)
                                }
                            }
                            SupportModelEnum.SAIPH_KKCAMERA.supportModel -> {
                                Log.i(TAG, "loadReactEnvironmentV3 ********** saiph_KKCamera")
                            }
                            SupportModelEnum.ELEVATOR.supportModel -> {
                                if (isSupportElevator) {
                                    Log.i(TAG, "loadReactEnvironmentV3 ********** elevator appId= ${it.appid} appName= ${it.appName}")
                                    pluginBundles.add(pluginPath)
                                }
                            }
                            SupportModelEnum.MEAL_FACTORY.supportModel -> {
                                if (isSupportFactory) {
                                    Log.i(TAG, "loadReactEnvironmentV3 ********** meal_factory appId= ${it.appid} appName= ${it.appName}")
                                    pluginBundles.add(pluginPath)
                                }
                            }
                            SupportModelEnum.SAIPH_MEAL.supportModel -> {
                                if (!isSupportElectricDoor && !isSupportFactory) {
                                    Log.i(TAG, "loadReactEnvironmentV3 ********** saiph_Meal appId= ${it.appid} appName= ${it.appName}")
                                    pluginBundles.add(pluginPath)
                                }
                            }
                            else -> {
                                Log.i(TAG, "loadReactEnvironmentV3 ********** appId= ${it.appid} appName= ${it.appName}")
                                pluginBundles.add(pluginPath)
                            }
                        }

//                        if (isNeedFilterOpk(it.appid)) {
//                            Log.i(TAG, "loadReactEnvironmentV3 ********** needFilterOpk appId= ${it.appid} appName= ${it.appName}")
//                            continue
//                        }
//                        pluginBundles.add(pluginPath)
                    }
                } else {
                    iterator.remove()
                }
            }
            Log.i(TAG, "loadReactEnvironmentV3 ********** ${pluginBundles.size}")
        }

//        AppManger.addApp(null, hostBundlePath, platformBundlePath, hostInfo!!)
//        hostInfo?.plugin?.forEach {
//            if (AppManger.isAppValid(it.appid)) {
//                val pluginInfo = AppManger.getRPKByCharacter(it.appid)
//                if (it.versionName != pluginInfo?.versionName) {
//                    FileHelper.copyDirectory(File("${pluginInfo!!.path}/extra"), File("${hostInfo.path}/extra"))
//                }
//
//                it.versionName = pluginInfo.versionName
//                it.path = pluginInfo.path
//
//                val pluginPath = AppManger.getAppBizPathByCharacter(it.appid)
//                if (File(pluginPath).exists()) {
//                    pluginBundles.add(pluginPath)
//                }
//            }
//        }

        Log.d(TAG, "Start host info : " + AppManger.getRPKByCharacter(startEvent.name))
        if (startEvent.msgId.isNotEmpty()) {
            reportOpkOpCmd(startEvent.msgId, startEvent.name, OpkOpStatus.RN_START_DOWNLOAD_RESOURCE)
        }

        Log.i(TAG, "platformBundlePath $platformBundlePath")
        ReactHelper.loadReactJS(BaseApplication.getApplication().reactNativeHost.reactInstanceManager, platformBundlePath!!, isCreate) {
            val catalystInstance = it?.catalystInstance
            if (catalystInstance == null) {
                Log.e("rpk", "catalystInstance is null")
                return@loadReactJS
            }

            //加载业务Workflow bundle文件
            Log.i(TAG, "hostBundlePath $hostBundlePath")
            BridgeUtil.loadScriptFromFile(hostBundlePath, catalystInstance, hostBundlePath, false)

            //加载业务bundle
            pluginBundles.forEach {
                Log.i(TAG, "pluginBundlePath: $it")
                if (RNClientManager.instance?.apiManager?.isMeissa() == true &&
                    it?.contains("system_906ba90d9e7822af7e43b12796401080") == true) {
                    Log.i(TAG, "ignore old chat in meissa");
                    return@forEach;
                }
                try {
                    BridgeUtil.loadScriptFromFile(it, catalystInstance, it, false)
                } catch (e: Exception) {
                    Log.i(TAG, "pluginBundlePath failed : $it  is exists ${File(it).exists()}")
                    e.printStackTrace()
                }
            }

            //React 页面创建
            <EMAIL>(null)
            //React Resume状态
            <EMAIL>()
            if (!startEvent.isCrash) {
                //上报切换opk是否成功
                RNClientManager.instance?.dataManager?.reportRNSwitch(1)
            }

            if (!TextUtils.isEmpty(startEvent.msgId)) {
                reportOpkOpCmd(startEvent.msgId, startEvent.name, OpkOpStatus.RN_OPK_START_SUCCESS)
            }
            addWaterMark()
        }

    }

    private fun isNeedFilterOpk(appid: String): Boolean {
        if (RNClientManager.instance != null && RNClientManager.instance!!.apiManager != null
            && RNClientManager.instance!!.apiManager!!.isSupportElectricDoor) {//电门版本
//            // 屏蔽海外送餐opk加载 OverSea_Meal system_fb42ebb15c361e29fd990c03aeb2a62a
//            if (TextUtils.equals(appid, "system_fb42ebb15c361e29fd990c03aeb2a62a")) {
//                return true
//            }
            // 加载速度优化，电动门版本只加载opk最小集(Utils, Portal, Edoor)
            // 海外希望放开Welcome, Leading, DistributeFood
            return !(TextUtils.equals(appid, "system_3f9e2b17ac061732f2b679ff9bdc9f52")
                    || TextUtils.equals(appid, "system_5413441859acdb7380efcc6701e1530a")
                    || TextUtils.equals(appid, "system_78315b107c868f7d79862c041400d146")
                    || TextUtils.equals(appid, "system_6c520a0aaf68c2e34ccdac7053c9803a")
                    || TextUtils.equals(appid, "system_8a3818b0c155b61b59f168c82c7422dd")
                    || TextUtils.equals(appid, "system_a7112917237ef8f4c03c3174cef7837c"))
        } else {//非电门版本
            // 屏蔽电动门opk加载 OverSea_Meal_Edoor system_78315b107c868f7d79862c041400d146
            if (TextUtils.equals(appid, "system_78315b107c868f7d79862c041400d146")) {
                return true
            }
        }

        if (RNClientManager.instance != null && RNClientManager.instance!!.apiManager != null
            && RNClientManager.instance!!.apiManager!!.isSupportKKCamera) {//线激光版本
            // 线激光版本，屏蔽：OverSea_Meal, Meal,
            return (TextUtils.equals(appid, "system_fb42ebb15c361e29fd990c03aeb2a62a")
                    || TextUtils.equals(appid, "system_853c3dd32487452feca5f0c69b3284a8"))
        } else {//非线激光版本
            // 屏蔽智能托盘opk-OverSea_Meal_Smart
            if (TextUtils.equals(appid, "system_7dd324bc868c229d75dc4ea76da56147")) {
                return true
            }
        }

        if ((RNClientManager.instance != null && RNClientManager.instance!!.apiManager != null
                    && RNClientManager.instance!!.apiManager!!.isSupportElevator) &&
            (RNClientManager.instance != null && RNClientManager.instance!!.robotSettingManager != null
                    && RNClientManager.instance!!.robotSettingManager!!.getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED) == Definition.ROBOT_SETTING_ENABLE)
        ) {
            // 屏蔽普通国内领位opk加载 Leading_mini system_fa1c059ee428a9d21adf393a1d8fa011
            if (TextUtils.equals(appid, "system_fa1c059ee428a9d21adf393a1d8fa011")) {
                return true
            }
        } else {
            // 屏蔽普通国内电梯领位opk加载 Elevator_Leading system_d184937448ac2e93fad165457e579add
            if (TextUtils.equals(appid, "system_d184937448ac2e93fad165457e579add")) {
                return true
            }
        }
        return false
    }

    /**
     * 加载RN环境 V2 拆包版本
     * */
    private fun loadReactEnvironmentV2(isCreate: Boolean, startEvent: StartEvent = StartEvent(false, -1, "", "")) {
        Log.i(TAG, "loadReactEnvironmentV1 $startEvent")
        Log.i(TAG, "loadReactEnvironmentV2 $isCreate " + startEvent.isCrash)
        val platformBundlePath = AppManger.getAppPlatformPathByCharacter(startEvent.name)
        val bizBundlePath = AppManger.getAppBizPathByCharacter(startEvent.name)
        val rpkBean = AppManger.getRPKByCharacter(startEvent.name)
        Log.i(TAG, "bizBundlePath $bizBundlePath")
        Log.i("RPK", "version ${rpkBean!!.versionName} appid: ${rpkBean.appid}")
        Log.i(TAG, "platformBundlePath $platformBundlePath")

        Log.d("rpk", "core begin...")
        FileDownloadLog.NEED_LOG = BaseApplication.getApplication().isDebug
        FileDownloader.setup(this)
        if (startEvent.msgId.isNotEmpty()) {
            reportOpkOpCmd(startEvent.msgId, startEvent.name, OpkOpStatus.RN_START_DOWNLOAD_RESOURCE)
        }

        val downloadCoreBundleHelper = DownloadCoreBundleBuilder(rpkBean!!.coreTarget!!, BaseApplication.getContext().filesDir.absolutePath, object : FileCoreListener {
            override fun completed(coreBundlePath: String?, isFromRemote: Boolean) {
                Log.d("rpk", "core js download success path: $coreBundlePath")
                if (!startEvent.isCrash) {
                    //crash 时候不上报
                    val source = if (isFromRemote) 2 else 1
                    if (coreBundlePath == null) {
                        if (!TextUtils.isEmpty(startEvent.msgId)) {
                            notifyToServer(startEvent.msgId, startEvent.name, 102, "core load failed")
                            reportOpkOpCmd(startEvent.msgId, startEvent.name, OpkOpStatus.RN_OPK_START_FAILED)
                        }
                        return
                    } else {
                        if (!TextUtils.isEmpty(startEvent.msgId)) {
                            reportOpkOpCmd(startEvent.msgId, startEvent.name, OpkOpStatus.RN_DOWNLOAD_SUCCESS)
                        }
                    }
                }
                val backupFile = File(OPKHelper.getBackupPath(rpkBean?.appid))
                //需要兼容之前无md5信息opk
                if (backupFile.exists() && rpkBean?.bizMd5 != null && rpkBean?.platformMd5 != null) {
                    Log.i(TAG, "current_bizMd5 " + Md5.generateFileMD5(bizBundlePath))
                    Log.i(TAG, "current_platformMd5 " + Md5.generateFileMD5(platformBundlePath))
                    if (rpkBean?.bizMd5 != Md5.generateFileMD5(bizBundlePath) || rpkBean?.platformMd5 != Md5.generateFileMD5(platformBundlePath)) {
                        Log.i(TAG, "opk v2 bundle use backup")
                        //解压备份文件
                        FileHelper.unzip(backupFile.absolutePath, File(bizBundlePath).parent)
                    }
                }
                ReactHelper.loadReactJS(BaseApplication.getApplication().reactNativeHost.reactInstanceManager, platformBundlePath!!, isCreate) {
                    val catalystInstance = it?.catalystInstance
                    if (catalystInstance == null) {
                        Log.e("rpk", "catalystInstance is null")
                        return@loadReactJS
                    }
                    //加载核心bundle文件
                    BridgeUtil.loadScriptFromFile(coreBundlePath, catalystInstance, coreBundlePath, false)
                    //加载业务bundle文件
                    BridgeUtil.loadScriptFromFile(bizBundlePath, catalystInstance, bizBundlePath, false)
                    //React 页面创建
                    <EMAIL>(null)
                    //React Resume状态
                    <EMAIL>()
                    if (!startEvent.isCrash) {
                        //上报切换opk是否成功
                        RNClientManager.instance?.dataManager?.reportRNSwitch(1)
                    }

                    if (!TextUtils.isEmpty(startEvent.msgId)) {
                        notifyToServer(startEvent.msgId, startEvent.name, 0)
                        reportOpkOpCmd(startEvent.msgId, startEvent.name, OpkOpStatus.RN_OPK_START_SUCCESS)
                    }
                    addWaterMark()
                }
            }

            override fun error(error: Int, message: String?) {
                Log.d("rpk", "core js download $message")
                ReportControl.getInstance().reportMsg(RnDownloadTargetPoint(99, 0, rpkBean!!.coreTarget!!))
                val errmsg = when (error) {
                    101 -> "core download failed"
                    102 -> "core load failed"
                    103 -> "core config err"
                    104 -> "opk not found"
                    else -> "unkownow"
                }
                if (!TextUtils.isEmpty(startEvent.msgId)) {
                    notifyToServer(startEvent.msgId, startEvent.name, error, errmsg)
                    reportOpkOpCmd(startEvent.msgId, startEvent.name, OpkOpStatus.RN_DOWNLOAD_FAILED)
                }
            }
        }).build()
        //crash时先使用缓存来加载core
        downloadCoreBundleHelper.setCache(startEvent.isCrash)
        downloadCoreBundleHelper.start()
    }

    @Subscriber(mode = ThreadMode.MAIN, tag = Constant.EVENT_BUS_DELETE_BUNDLE_ENVIRONMENT)
    fun deleteTmpBundle() {
        var tempBundle = File(this.filesDir, "ReactNativeDevBundle.js");
        if (tempBundle.exists()) {
            tempBundle.delete();
        }
    }

    @Subscriber(mode = ThreadMode.MAIN, tag = Constant.EVENT_BUS_REMOTE_DEBUG_ENVIRONMENT)
    fun setRemoteDebug(enable: Boolean) {
        val devManager = reactNativeHost.reactInstanceManager.devSupportManager;
        Log.d(TAG, "Set remote debug : " + enable + "     " + devManager.devSettings.isRemoteJSDebugEnabled);
        if (enable == devManager.devSettings.isRemoteJSDebugEnabled) {
            return;
        }
        devManager.devSettings.setRemoteJSDebugEnabled(enable)
        devManager.handleReloadJS()
    }

    @Subscriber(mode = ThreadMode.MAIN, tag = Constant.EVENT_BUS_DEBUG_CHARACTER_ENVIRONMENT)
    fun onDebugCharacterEnvironment(isDebug: Boolean) {
        Log.i(TAG, "onDebugCharactorEnvironment $isDebug")

        //将当前的ReactRootView 从ReactInstanceManager中卸载
        super.onReactNativeHostDestory()
        //将之前设置的ReactNative环境全部销毁
        BaseApplication.getApplication().reactNativeHost.clear()
        //由于调用了clear()方法，所以Activity会被释放
        //由于重新加载React环境，所以需要等待初始化完成才会有在Resume状态中才会Activity设置进去。
        //防止onPause崩溃
        <EMAIL>()
        if (isDebug) {
            //重建ReactNative环境（重新被设置成UseDeveloperSupport）
            loadReactEnvironmentDeveloperSupport(true)
            addDebugModeMark()
        } else {
            removeDebugModeMark()
            //重新从本地获取当前角色OPK文件加载并运行
            val opkBeanV2 = AppManger.getRPKByCharacter(ReactCharacter.currentCharacter)
            OPKHelper.onHandleWithOPK(opkBeanV2!!, {
                loadReactEnvironmentV1(true)
            }, {
                //由于ReactRootView已经attach 且又是拆包，则需要先detach ReactRootView
                //多次setContentView只要没有过场动画 Content就会移除所有的View
                loadReactEnvironmentV2(true)
            }, {
                loadReactEnvironmentV3(true)
            })
        }
    }

    /**
     * 重新加载RN环境
     * */
    private fun onReloadReactNativeEnvironment(startEvent: StartEvent) {
        Log.i(TAG, "onReloadReactNativeEnvironment $startEvent")
        //如果不存在合法的OPK
        if (!AppManger.isAppValid(startEvent.name)) {
            Log.i(TAG, "opk ${startEvent.name} is not valid")
            //且UseDeveloperSupport 则使用hot reload 模式

            if (!TextUtils.isEmpty(startEvent.msgId)) {
                notifyToServer(startEvent.msgId, startEvent.name, 104, "opk not found")
            }

            if (BaseApplication.getApplication().reactNativeHost.useDeveloperSupport) {
                loadReactEnvironmentDeveloperSupport(false)
            }
            return
        }
        val opkBeanV2 = AppManger.getRPKByCharacter(startEvent.name)
        OPKHelper.onHandleWithOPK(opkBeanV2!!, {
            loadReactEnvironmentV1(false, startEvent = startEvent)
        }, {
            //由于ReactRootView已经attach 且又是拆包，则需要先detach ReactRootView
            //多次setContentView只要没有过场动画 Content就会移除所有的View
            super.onReactNativeHostDestory()
            loadReactEnvironmentV2(false, startEvent = startEvent)
        }, {
            super.onReactNativeHostDestory()
            loadReactEnvironmentV3(false, startEvent = startEvent)
        })
    }

    private fun notifyToServer(msgId: String, name: String, result: Int, errmsg: String? = null) {
        Log.d(TAG, "notifyToServer reqId: $msgId result: $result name: $name")
        //上报
        BackgroundThread.post(Runnable {
            try {
                val response = NetworkManger.instance.getLunchReportRequest().launchReport(msgId, result, errmsg
                    ?: "", SystemUtils.getSystemSerialNo(), name, "cmd_launch_rnapp", RobotSettings.getVersion()).execute()
                if (response.isSuccessful) {
                    val jRoots = response.body()
                    val header = jRoots?.getJSONObject("header")
                    if (header?.getInt("code") == 0) {
                        Log.w(TAG, "notifyToServer success")
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        })
    }

    private fun startEmojiView() {
        var readableMap = WritableNativeMap()
        Log.i("onEventBus_EmojiCreated", "WritableNativeMap")
        ReactNativeEventEmitter.triggerEvent("onEmojiCreated", readableMap)
    }

    override fun onNewIntent(intent: Intent) {
        Log.i(TAG, "onNewIntent: $intent")
        val reqId = intent.getIntExtra("extra_reqId", -1)
        val msgId = intent.getStringExtra("extra_msgId")
        val name = intent.getStringExtra("extra_name")
        val isCrash = intent.getBooleanExtra("extra_crash", false)

        if (!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus()) {
            if (ReactCharacter.currentCharacter?.equals(name) ?: false) {
                Log.d(TAG, "onNewIntent current already is $name")
                return;
            }
        }

        ReactCharacter.currentCharacter = name
        Log.d(TAG, "onNewIntent reqId: $reqId msgId: $msgId name: $name")
        StatusBarManager.getStatusBar()?.onCreate();
        if (reqId < 0) {
            return
        }

        <EMAIL>();

//        val handler = Handler()
//        handler.post(object : Runnable {
//            override fun run() {
//                if (RNClientManager.instance!!.mRNManagerService != null) {
//                    val currentCharcater = RNClientManager.instance?.dataManager?.currentCharcater
//                    Log.i(TAG, "opk $currentCharcater name: is valid")
//                    if (currentCharcater.isNullOrEmpty()) {
//                        ClientUtils.killProcess(Process.myPid())
//                        return
//                    }
//                    var tmpMsgId = ""
//                    if (!msgId.isNullOrEmpty()) {
//                        tmpMsgId = msgId
//                    }
//                    val startEvent = StartEvent(isCrash, reqId, tmpMsgId, name)
//                    onReloadReactNativeEnvironment(startEvent)
//                } else {
//                    handler.postDelayed(this, 100)
//                }
//            }
//
//        })
    }

    override fun onDestroy() {
        Log.i(TAG, "onDestroy")
        StatusBarManager.getStatusBar().onDestroy();
        EventBus.getDefault().unregister(this)
        super.onDestroy()
        ClientUtils.killProcess(Process.myPid())
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        Log.i(TAG, "onKeyDown keyCode:$keyCode,event:$event")
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        Log.i(TAG, "dispatchKeyEvent event:$event")
        val readableMap = WritableNativeMap()
        readableMap.putInt("deviceId", event!!.deviceId);
        readableMap.putInt("source", event!!.source);
        readableMap.putInt("metaState", event!!.metaState);
        readableMap.putInt("action", event!!.action);
        readableMap.putInt("keyCode", event!!.keyCode);
        readableMap.putInt("scanCode", event!!.scanCode);
        readableMap.putInt("repeatCount", event!!.repeatCount);
        readableMap.putInt("flags", event!!.flags);
        readableMap.putString("downTime", event!!.downTime.toString());
        readableMap.putString("eventTime", event!!.eventTime.toString());
        readableMap.putString("characters", event!!.characters);
        val keyCode = event.getKeyCode();
        Log.i(TAG, "dispatch keyCode:$keyCode")
        if (event.keyCode == KeyEvent.KEYCODE_VOLUME_UP) {
            if (event.action == KeyEvent.ACTION_DOWN) {
                return true
            } else if (event.action == KeyEvent.ACTION_UP) {
                return true
            }
        } else if (event.keyCode == KeyEvent.KEYCODE_VOLUME_DOWN) {
            if (event.action == KeyEvent.ACTION_DOWN) {
                return true
            } else if (event.action == KeyEvent.ACTION_UP) {
                return true
            }
        }
        ReactNativeEventEmitter.triggerEvent("onKeyEvent", readableMap);
        return super.dispatchKeyEvent(event)
    }

    private var x1: Float = 0.0f
    private var y1: Float = 0.0f
    private var x2: Float = 0.0f
    private var y2: Float = 0.0f
    private var isPress: Boolean = false
    private var lastClickTime: Long = 0

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        Log.d(TAG, "action: " + event.action + " type: " + event.getToolType(0))
        when (event.getToolType(0)) {
            MotionEvent.TOOL_TYPE_STYLUS -> {
                // 处理触控笔事件
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        if (!isPress) {
                            x1 = event.x
                            y1 = event.y
                            isPress = true
                            return true
                        } else {
                            reset()
                            Log.i(TAG, "down, down")
                        }
                    }

                    MotionEvent.ACTION_UP -> {
                        if (isPress) {
                            x2 = event.x
                            y2 = event.y
                            if (handleTouch(x1, y1, x2, y2)) {
                                reset()
                                return true
                            } else {
                                Log.i(TAG, "motion err, down[$x1, $y1] --> up[$x2, $y2]")
                                reset()
                            }
                        } else {
                            Log.i(TAG, "up, up")
                            reset()
                        }
                    }
                }
            }

            else -> {
                // 处理其他类型的事件
                ReactNativeEventEmitter.triggerEvent("onTouchEvent", event.action);
                Log.i(TAG, "dispatchTouchEvent: " + event.action)
            }
        }
        return super.dispatchTouchEvent(event)
    }
    private fun reset() {
        x1 = 0.0f
        y1 = 0.0f
        x2 = 0.0f
        y2 = 0.0f
        isPress = false
    }
    private fun handleTouch(x1: Float, y1: Float, x2: Float, y2: Float): Boolean {
        var handleKey: Int = handleTouchDirection(x1, y1, x2, y2)
        Log.d(TAG, "handleKey: $handleKey")
        if (handleKey != HANDLE_CENTER_KEY) {
            ReactNativeEventEmitter.triggerEvent(EVENT_NAME, handleKey)
        }
        return handleKey != HANDLE_ERROR
    }

    private fun handleTouchDirection(x1: Float, y1: Float, x2: Float, y2: Float): Int {
        Log.d(TAG, "handleTouchDirection x1: $x1 x2: $x2 y1: $y1, y2: $y2")
        val deltaX = x2 - x1
        val deltaY = y2 - y1
        if (abs(deltaX) <= 10 && abs(deltaY) <= 10) {
            return onSwipeCenter() // 中间按键区分了双击和单击 点击在这里处理
        } else if (abs(deltaX) > abs(deltaY)) {
            if (abs(deltaY) <= 10) {
                if (deltaX > 0) {
                    return onSwipeLeft()
                } else {
                    return onSwipeRight()
                }
            }
        } else {
            if (abs(deltaX) <= 10) {
                if (deltaY > 0) {
                    return onSwipeUp()
                } else {
                    return onSwipeDown()
                }
            }
        }
        return HANDLE_ERROR
    }

    private val handler = Handler(Looper.getMainLooper())
    private fun onSwipeCenter(): Int{
        Log.i(TAG, "onSwipeCenter")
        val clickTime = SystemClock.uptimeMillis()
        val diffTime: Long = clickTime - lastClickTime
        Log.i(TAG, "clickTime: $clickTime lastClickTime: $lastClickTime diffTime: $diffTime")
        if (diffTime <= DOUBLE_CLICK_TIME_DELTA ) {
            // 在双击间隔时间内再次点击，判断为双击事件
            handler.removeCallbacksAndMessages(null) // 移除之前的点击事件处理
            lastClickTime = 0
            ReactNativeEventEmitter.triggerEvent(EVENT_NAME, HANDLE_CENTER_KEY_LONG)
        } else {
            lastClickTime = clickTime
            // 延时处理单击事件，等待双击事件判断
            handler.postDelayed({
                ReactNativeEventEmitter.triggerEvent(EVENT_NAME, HANDLE_CENTER_KEY_SHORT)
            }, DOUBLE_CLICK_TIME_DELTA)
        }
        return HANDLE_CENTER_KEY
    }

    private fun onSwipeUp(): Int {
        // Handle swipe up
        Log.i(TAG, "onSwipeUp")
        return HANDLE_UP_KEY
    }

    private fun onSwipeDown(): Int {
        // Handle swipe down
        Log.i(TAG, "onSwipeDown")
        return HANDLE_DOWN_KEY
    }

    private fun onSwipeLeft(): Int {
        // Handle swipe left
        Log.i(TAG, "onSwipeLeft")
        return HANDLE_LEFT_KEY
    }

    private fun onSwipeRight(): Int {
        // Handle swipe right
        Log.i(TAG, "onSwipeRight")
        return HANDLE_RIGHT_KEY
    }

    override fun onPause() {
        super.onPause()
        SwitchEmojiManager.getInstance().onPause()
        Log.i(TAG, "onPause")
        if (RNClientManager.instance?.apiManager != null) {
            RNClientManager.instance?.apiManager?.unregisterStatusListener(mBatteryListener)
        }
        if (mBatteryWarningDialog != null) {
            mBatteryWarningDialog?.dismiss()
            mBatteryWarningDialog = null
        }
        mFirstCheckBattery = false
        if (mBatteryNotifyDialog != null) {
            mBatteryNotifyDialog?.dismiss()
            mBatteryNotifyDialog = null
        }
    }

    fun onSuspend() {
        //TODO: 暂停RN页面
        Log.d(TAG, "on suspend");
        ReactNativeEventEmitter.triggerEvent("_CHARACTER_SUSPEND_", "")
    }

    fun onRecovery() {
        Log.d(TAG, "on recovery");
        ReactNativeEventEmitter.triggerEvent("_CHARACTER_RECOVERY_", "")

        //恢复权限时强制刷新非系统OPK
        val currentCharcater = ReactCharacter.currentCharacter
        val appInfo = AppManger.getRPKByCharacter(currentCharcater)

        if (appInfo != null && !appInfo.isSystem) {
            Log.d(TAG, "recreate ********");
            runOnUiThread {
                this.recreate()
            }
        }
    }

    override fun onStart() {
        super.onStart()
        StatusBarManager.getStatusBar()?.onStart(this);
        Log.i(TAG, "onStart")
    }

    override fun onStop() {
        super.onStop()
        Log.i(TAG, "onStop")
        StatusBarManager.getStatusBar()?.onStop();

    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.i(TAG, "onConfigurationChanged $newConfig")

    }

}
