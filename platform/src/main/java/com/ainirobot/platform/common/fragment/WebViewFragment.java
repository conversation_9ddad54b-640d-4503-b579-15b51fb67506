package com.ainirobot.platform.common.fragment;

import static com.facebook.react.bridge.UiThreadUtil.runOnUiThread;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.AssetManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.GeolocationPermissions;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.speech.SkillApi;
import com.ainirobot.platform.BaseApplication;
import com.ainirobot.platform.R;
import com.ainirobot.platform.common.KProgressBar;
import com.ainirobot.platform.nlp.state.NlpStateManager;
import com.ainirobot.platform.speech.SpeechApi;
import com.google.gson.Gson;

import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import org.json.JSONException;
import org.json.JSONObject;

public class WebViewFragment extends Fragment {
    private static final String TAG = "WebViewFragment";
    private static final long RECONNECT_INTERVAL = 5000;
    private final SkillApi skillApi = new SkillApi();
    private Timer timer;
    private static final String ARG_PARAMS = "params";
    private static final String TEST_SPECIAL_DOMAIN = "test-agentpoi.orionstar.com";
    private static final String PRO_SPECIAL_DOMAIN = "agentpoi.orionstar.com";

    private WebView webView;
    private View progressBar;
    private ImageView backBtn, forwardBtn, refreshBtn, closeBtn;
    private WebViewCallback callback;
    private WebViewParams cachedParams = null;
    private boolean isErrorPage = false;
    private long pageLoadStartTime = 0;

    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    private Context context;

    public static WebViewFragment newInstance(String params) throws JSONException {
        WebViewFragment fragment = new WebViewFragment();
        Bundle args = new Bundle();
        args.putString(ARG_PARAMS, params);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context = context;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        connectSpeechServer();
        Log.d(TAG, "onCreate");
    }


    private void connectSpeechServer() {
        ApiListener listener = new ApiListener() {
            @Override
            public void handleApiDisabled() {

            }

            @Override
            public void handleApiConnected() {
                SpeechApi.getInstance().setSpeechApi(skillApi);
                NlpStateManager.Companion.get().resetNlpLauncher();
            }

            @Override
            public void handleApiDisconnected() {
                reconnect();
            }
        };
        skillApi.addApiEventListener(listener);
        skillApi.connectApi(BaseApplication.getContext());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView");
        View view = inflater.inflate(R.layout.fragment_webview, container, false);
        initViews(view);
        setupWebView();
        return view;
    }


    private void reconnect() {
        cancelTimer();
        timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.e(TAG, "Reconnect to speech");
                if (skillApi.isApiConnectedService()) {
                    Log.e(TAG, "Already connected to speech");
                    cancelTimer();
                    return;
                }
                skillApi.connectApi(BaseApplication.getContext());
            }
        }, 0, RECONNECT_INTERVAL);
    }

    private void initViews(View view) {
        Log.d(TAG, "initViews");
        webView = view.findViewById(R.id.web_view);
        progressBar = view.findViewById(R.id.progress_bar);
        backBtn = view.findViewById(R.id.back_btn);
        forwardBtn = view.findViewById(R.id.forward_btn);
        refreshBtn = view.findViewById(R.id.refresh_btn);
        closeBtn = view.findViewById(R.id.close_btn);
        setupClickListeners();
    }

    private void cancelTimer() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    private void setupClickListeners() {
        Log.d(TAG, "Setting up click listeners");

        closeBtn.setOnClickListener(v -> {
            Log.d(TAG, "Close button clicked");
            if (callback != null) {
                Log.d(TAG, "callback");
                callback.onClose(true);
            }
        });

        backBtn.setOnClickListener(v -> {
            Log.d(TAG, "Back button clicked, canGoBack: " + (webView != null && webView.canGoBack()));
            if (webView != null && webView.canGoBack()) {
                webView.goBack();
            }
        });

        forwardBtn.setOnClickListener(v -> {
            Log.d(TAG, "Forward button clicked, canGoForward: " + (webView != null && webView.canGoForward()));
            if (webView != null && webView.canGoForward()) {
                webView.goForward();
            }
        });

        refreshBtn.setOnClickListener(v -> {
            Log.d(TAG, "Refresh button clicked");
            if (webView != null) {
                webView.reload();
            }
        });
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        try {
            Log.d(TAG, "Setting up WebView");
            WebSettings settings = webView.getSettings();
            settings.setJavaScriptEnabled(true);
            settings.setJavaScriptCanOpenWindowsAutomatically(true);
            settings.setDomStorageEnabled(true);
            settings.setAllowFileAccess(true);
            settings.setAllowContentAccess(true);
            settings.setUseWideViewPort(true);
            settings.setLoadWithOverviewMode(true);
            settings.setGeolocationEnabled(true);
            settings.setDatabaseEnabled(true);
            // 启用缩放支持，允许程序化缩放
            settings.setSupportZoom(false);
            settings.setDisplayZoomControls(false);
            settings.setCacheMode(WebSettings.LOAD_DEFAULT);
            settings.setAppCacheEnabled(true);
            // 启用内置缩放控件但隐藏显示
            settings.setBuiltInZoomControls(true);
            // 设置缩放范围，防止用户过度缩放
            webView.setInitialScale(1);  // 临时设置，会被settingScale覆盖
            settings.setAppCachePath(requireContext().getCacheDir().getAbsolutePath());
            settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NORMAL);
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
            settings.setLoadsImagesAutomatically(true);
            webView.setWebViewClient(createWebViewClient());
            settings.setUserAgentString("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            webView.setWebChromeClient(createWebChromeClient());
            webView.removeJavascriptInterface("Android");
            webView.addJavascriptInterface(new WebViewInterface(), "Android");
            WebViewParams params = getWebViewParams();
            if (!params.url.isEmpty()) {
                Log.d(TAG, "Loading initial URL: " + params.url + " with scale: " + params.scale);
                loadUrl(params.toString());
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up WebView: " + e.getMessage());
            if (callback != null) {
                callback.onError("Failed to setup WebView: " + e.getMessage());
            }
        }
    }


    private WebViewClient createWebViewClient() {
        return new WebViewClient() {
            private boolean isRedirect = false;

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (url.startsWith("mailto:")) {
                    return true;
                }
                return false;
            }

            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                Log.d(TAG, "onPageStarted: " + url);
                super.onPageStarted(view, url, favicon);
                // 记录开始时间
                isErrorPage = false;
                isRedirect = false;
                pageLoadStartTime = System.currentTimeMillis();
                WebViewParams params = getWebViewParams();
                settingScale(url, (int) params.scale);
                mainHandler.post(() -> {
                    if (progressBar != null) {
                        progressBar.setVisibility(View.VISIBLE);
                    }
                });
                if (callback != null) {
                    callback.onPageStarted(url);
                }
            }


            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                try {
                    java.net.URL urlObj = new java.net.URL(url);
                    if (!TEST_SPECIAL_DOMAIN.equals(urlObj.getHost()) && !PRO_SPECIAL_DOMAIN.equals(urlObj.getHost())) {
                        WebViewParams params = getWebViewParams();
                        // 确保缩放设置生效
                        settingScale(url, (int) params.scale);
                    }
                } catch (MalformedURLException e) {
                    Log.e(TAG, "onPageFinished: Invalid URL: " + url, e);
                }

                if (isRedirect || url.startsWith("file:///android_asset/")) {
                    return;
                }
                long loadEndTime = System.currentTimeMillis();
                long loadDuration = loadEndTime - pageLoadStartTime;
                Log.d(TAG, "onPageFinished: " + url + ", loadTime: " + loadDuration + "ms"+", isRedirect: " + isRedirect);
                mainHandler.post(() -> {
                    if (progressBar != null) {
                        progressBar.setVisibility(View.GONE);
                    }
                });
                String jsCode = readJavaScriptFile(context, "webviewJs_old.js");
                view.evaluateJavascript(jsCode, result -> {
                    Log.d(TAG, "evaluateJavascript result: " + result);
                });
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                Log.d(TAG, "shouldOverrideUrlLoading！");
                WebViewParams webViewParams = getWebViewParams();
                // 1. 空安全检查
                if (request == null) {
                    Log.w(TAG, "shouldOverrideUrlLoading, delegate to super");
                    return super.shouldOverrideUrlLoading(view, request);
                }
                // 2. 获取 Uri 并检查
                Uri uri = request.getUrl();
                if (uri == null) {
                    Log.w(TAG, "shouldOverrideUrlLoading=====Null Uri in WebResourceRequest");
                    return super.shouldOverrideUrlLoading(view, request);
                }
                // 3. 转换为字符串并记录日志
                String url = uri.toString();
                Log.d(TAG, "shouldOverrideUrlLoading: " + url);

                // 4. 协议过滤
                if (!url.startsWith("http:") && !url.startsWith("https:")) {
                    Log.d(TAG, "shouldOverrideUrlLoading=====Non-HTTP(s) protocol, delegate to system");
                    return super.shouldOverrideUrlLoading(view, request);
                }
                // 5. 处理重定向逻辑
                isRedirect = true;
                if (view != null) {
                    WebViewFragment.this.settingScale(url, (int) webViewParams.scale);
                    view.loadUrl(url);
                }
                return true;
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                Log.d(TAG, "onReceivedError: " + request.getUrl() + ", " + error.getErrorCode());
                WebViewParams webViewParams = getWebViewParams();
                if (request.isForMainFrame()) {
                    isErrorPage = true;
                    mainHandler.post(() -> {
                        if (progressBar != null) {
                            progressBar.setVisibility(View.VISIBLE);
                        }
                    });
                    // 根据系统语言选择不同的错误页面
                    String language = Locale.getDefault().toString();
                    webView.setInitialScale((int) webViewParams.scale);
                    if (language.startsWith("zh_CN")) {
                        view.loadUrl("file:///android_asset/error_domestic.html");
                    } else {
                        view.loadUrl("file:///android_asset/error_oversea.html");
                    }
                }
                if (callback != null) {
                    callback.onPageFinished(webViewParams.toString());
                }
            }
        };
    }

    private WebChromeClient createWebChromeClient() {
        return new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                if (progressBar instanceof KProgressBar) {
                    mainHandler.post(() -> {
                        ((KProgressBar) progressBar).setProgress(newProgress);
                    });
                }
            }

            @Override
            public void onGeolocationPermissionsShowPrompt(String origin, GeolocationPermissions.Callback callback) {
                callback.invoke(origin, true, false);
            }
        };
    }

    public void goBack() {
        runOnUiThread(() -> {
            if (webView != null && webView.canGoBack()) {
                webView.goBack();
            }
        });
    }

    public void goForward() {
        runOnUiThread(() -> {
            if (webView != null && webView.canGoForward()) {
                webView.goForward();
            }
        });
    }

    public void reload() {
        runOnUiThread(() -> {
            if (webView != null) {
                webView.reload();
            }
        });
    }

    public void loadUrl(String params){
        updateParams(params);
        WebViewParams webViewParams = getWebViewParams();
        if (webView != null) {
            Log.d(TAG, "Loading loadUrl URL: " + params);
            try {
                mainHandler.post(() -> {
                    // 显示进度条
                    if (progressBar != null) {
                        progressBar.setVisibility(View.VISIBLE);
                    }
                    // 在主线程中设置缩放和加载URL
                    settingScale(webViewParams.url, (int) webViewParams.scale);
                    // 重置状态
                    isErrorPage = false;
                    pageLoadStartTime = System.currentTimeMillis();
                    // 加载新URL
                    webView.loadUrl(webViewParams.url);
                });
            } catch (Exception e) {
                Log.e(TAG, "Error loading URL: " + e.getMessage());
                if (callback != null) {
                    callback.onError("Failed to load URL: " + e.getMessage());
                }
            }
        } else {
            Log.e(TAG, "WebView is null when trying to load URL: " + webViewParams.url);
        }
    }

    /**
     * 模拟点击网页中的元素
     * @param index 要点击的元素索引
     */
    public void simulateClick(int index) {
        if (webView != null) {
            try {
                Log.d(TAG, "simulateClick: index=" + index);
                // 准备点击数据
                Map<String, Object> data = new HashMap<>();
                data.put("type", "click");
                data.put("index", index);
                String json = new Gson().toJson(data);
                WebViewParams webViewParams = getWebViewParams();
                // 执行JavaScript点击操作
                webView.evaluateJavascript("javascript:receiveMessage('" + json + "')", result -> {
                    settingScale(webView.getUrl(), (int) webViewParams.scale);
                });
            } catch (Exception e) {
                Log.e(TAG, "Error simulating click: " + e.getMessage(), e);
                if (callback != null) {
                    callback.onError("Failed to simulate click: " + e.getMessage());
                }
            }
        } else {
            Log.e(TAG, "WebView is null when trying to simulate click");
        }
    }



    public void settingScale(String url, int scale) {
        try {
            java.net.URL urlObj = new java.net.URL(url);
            // 只有当域名不是这两个特殊域名时才设置缩放
            if (!TEST_SPECIAL_DOMAIN.equals(urlObj.getHost()) && !PRO_SPECIAL_DOMAIN.equals(urlObj.getHost())) {
                // 设置初始缩放比例，这对新加载的页面有效
                webView.setInitialScale(scale);
                float viewportScale = scale / 100.0f;
                String jsCode = "var meta = document.querySelector('meta[name=\"viewport\"]') || " +
                        "(function(){var m = document.createElement('meta'); m.name='viewport'; " +
                        "document.head.appendChild(m); return m;})(); " +
                        "meta.content = 'width=device-width, initial-scale=" + viewportScale +
                        ", maximum-scale=" + viewportScale + ", minimum-scale=" + viewportScale +
                        ", user-scalable=no'; " +
                        "Object.defineProperty(meta, 'content', {value: meta.content, writable: false, configurable: false}); " +
                        "meta.setAttribute = function(name) { if (name !== 'content') Element.prototype.setAttribute.apply(this, arguments); };";

                webView.evaluateJavascript(jsCode, null);
                Log.d(TAG, "settingScale: Set scale to " + scale + "% for domain: " + urlObj.getHost());
            } else {
                Log.d(TAG, "settingScale: Skip scaling for special domain: " + urlObj.getHost());
            }
        } catch (MalformedURLException e) {
            Log.e(TAG, "settingScale: Invalid URL: " + url, e);
        }
    }

    private String readJavaScriptFile(Context context, String fileName) {
        try {
            AssetManager assetManager = context.getAssets();
            InputStream input = assetManager.open(fileName);
            int size = input.available();
            byte[] buffer = new byte[size];
            input.read(buffer);
            input.close();
            return new String(buffer, "UTF-8");
        } catch (IOException e) {
            Log.e(TAG, "Error reading JavaScript file: " + e.getMessage(), e);
            return "";
        }
    }

    private class WebViewInterface {
        @JavascriptInterface
        public void onMessage(String message) {
            mainHandler.post(() -> {
                Log.d(TAG, "onWebViewMessage: " + message);
                if (callback != null) {
                    if (message.startsWith("dom:")) {
                        if (isErrorPage) {
                            callback.onMessage("webview_dom", "<Page Error, Content is Null>");
                        } else {
                            callback.onMessage("webview_dom", message.substring(4));
                        }
                    }
                    else if (message.startsWith("anchor:")) {
                        String textContent = message.substring(7)
                                .replaceAll("[\\n\\r]", " ")
                                .replaceAll("\\s+", " ")
                                .trim();
                        callback.onMessage("anchor", textContent);
                    }
                }
            });
        }

        @JavascriptInterface
        public void onExecuteFinish() {
            mainHandler.post(() -> {
                if (callback != null) {
                    WebViewParams params = getWebViewParams();
                    callback.onPageFinished(params.toString());
                }
            });
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume");
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause");
    }

    @Override
    public void onDestroyView() {
        Log.d(TAG, "onDestroyView");
        if (webView != null) {
            webView.stopLoading();
            webView.clearHistory();
            ((ViewGroup) webView.getParent()).removeView(webView);
            webView.destroy();
            webView = null;
        }
        callback = null;
        super.onDestroyView();
    }

    public void setCallback(WebViewCallback callback) {
        this.callback = callback;
    }

    public interface WebViewCallback {
        void onPageStarted(String url);
        void onPageFinished(String params);
        void onClose(boolean updatePage);
        void onMessage(String tag, String message);
        void onError(String error);
        void onExecuteFinish();
    }

    private static class WebViewParams {
        final String url;
        final float scale;
        final String sid;

        private WebViewParams(String url, float scale, String sid) {
            this.url = url;
            this.scale = scale;
            this.sid = sid;
        }

        static WebViewParams parseParams(String params) {
            try {
                JSONObject jsonObject = new JSONObject(params);
                return new WebViewParams(
                        jsonObject.optString("app_url", ""),
                        (float) jsonObject.optDouble("scale", 50.0),
                        jsonObject.optString("sid", "")
                );
            } catch (JSONException e) {
                Log.e(TAG, "Error parsing params: " + e.getMessage());
                return new WebViewParams("", 50.0F, "");
            }
        }

        @Override
        public String toString() {
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("app_url", url);
                jsonObject.put("scale", scale);
                jsonObject.put("sid", sid);
                return jsonObject.toString();
            } catch (JSONException e) {
                Log.e(TAG, "Error converting to JSON: " + e.getMessage());
                return "{}";
            }
        }
    }


    private WebViewParams getWebViewParams() {
        if (cachedParams == null) {
            String params = getArguments().getString(ARG_PARAMS);
            cachedParams = WebViewParams.parseParams(params);
        }
        return cachedParams;
    }

    public void updateParams(String params) {
        Bundle args = getArguments();
        if (args != null) {
            args.putString(ARG_PARAMS, params);
            cachedParams = null;
        }
    }
}
